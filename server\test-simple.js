console.log('🚀 Starting simple test server...');

const http = require('http');

const server = http.createServer((req, res) => {
    console.log(`📥 Request: ${req.method} ${req.url}`);
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end('<h1>Simple Test Server</h1><p>Server is working!</p>');
});

const PORT = 3000;
console.log(`🎯 About to listen on port ${PORT}...`);

server.listen(PORT, () => {
    console.log(`✅ Simple server running on http://localhost:${PORT}`);
});

server.on('error', (err) => {
    console.error('❌ Server error:', err);
});

console.log('📝 Script execution completed');
