require('dotenv').config();
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const cors = require('cors');
const path = require('path');

// Initialize express app
const app = express();
app.use(cors());
app.use(express.json());

// Serve TV app at /tv
app.use('/tv', express.static(path.join(__dirname, '../tv-app/www')));

// Serve mobile client at /mobile
app.use('/mobile', express.static(path.join(__dirname, '../mobile-client')));

// Serve video clips
app.use('/clips', express.static(path.join(__dirname, 'clips')));

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server with ping-pong heartbeat
const wss = new WebSocket.Server({
    server,
    path: '/ws',
    clientTracking: true
});

// Connection tracking
const connections = new Map();

// Game state
const gameState = {
    players: new Map(),
    currentRound: 0,
    maxRounds: 10,
    mode: null,
    isPlaying: false,
    clips: [], // Will store clip information
    answers: [], // Will store correct answers for each clip
    lastUpdateTime: Date.now()
};

// Heartbeat configuration
const HEARTBEAT_INTERVAL = 30000;
const CLIENT_TIMEOUT = 35000;

function heartbeat() {
    this.isAlive = true;
}

// Ping all clients periodically
const interval = setInterval(() => {
    wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
            handleDisconnect(ws);
            return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
    });
}, HEARTBEAT_INTERVAL);

wss.on('close', () => {
    clearInterval(interval);
});

// Handle WebSocket connections
wss.on('connection', (ws, req) => {
    console.log('New client connected');

    // Setup connection tracking
    ws.isAlive = true;
    ws.id = generateConnectionId();
    ws.connectionTime = Date.now();
    connections.set(ws.id, ws);

    // Setup heartbeat
    ws.on('pong', heartbeat);

    // Send initial game state with reconnection support
    sendGameState(ws);

    // Handle messages from clients
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            handleMessage(ws, data);
        } catch (error) {
            handleError(ws, error);
        }
    });

    // Handle client disconnection
    ws.on('close', () => {
        handleDisconnect(ws);
    });

    // Handle errors
    ws.on('error', (error) => {
        handleError(ws, error);
    });
});

// Enhanced message handler with routing
function handleMessage(ws, message) {
    try {
        if (!message.type) {
            throw new Error('Message type is required');
        }

        const handlers = {
            register: () => {
                const data = message.data;
                if (data.clientType === 'tv') {
                    // Handle TV client registration separately
                    sendGameState(ws);
                } else {
                    registerPlayer(ws, data);
                }
            },
            answer: () => handleAnswer(ws, message.data),
            startGame: () => startGame(message.data),
            endGame: () => endGame(),
            reconnect: () => handleReconnection(ws, message.data),
            sync: () => syncGameState(ws),
        };

        const handler = handlers[message.type];
        if (!handler) {
            throw new Error(`Unknown message type: ${message.type}`);
        }

        handler();
    } catch (error) {
        handleError(ws, error);
    }
}

// Handle client errors
function handleError(ws, error) {
    console.error('Client error:', error);

    ws.send(JSON.stringify({
        type: 'error',
        data: {
            message: 'An error occurred',
            code: error.code || 500
        }
    }));
}

// Enhanced player registration with reconnection support
function registerPlayer(ws, data) {
    const playerId = data.reconnectId || generatePlayerId();

    const playerData = {
        id: playerId,
        name: data.name,
        avatar: data.avatar,
        score: 0,
        connectionId: ws.id,
        lastActive: Date.now()
    };

    gameState.players.set(ws, playerData);

    // Send confirmation to the player
    ws.send(JSON.stringify({
        type: 'registered',
        data: {
            playerId,
            gameState: getPublicGameState()
        }
    }));

    // Notify all clients of new player
    broadcast({
        type: 'playerJoined',
        data: {
            playerCount: gameState.players.size,
            players: Array.from(gameState.players.values())
        }
    });
}

// Handle player reconnection
function handleReconnection(ws, data) {
    const { playerId } = data;
    const existingPlayer = Array.from(gameState.players.entries())
        .find(([_, player]) => player.id === playerId);

    if (existingPlayer) {
        const [oldWs, playerData] = existingPlayer;
        gameState.players.delete(oldWs);
        playerData.connectionId = ws.id;
        playerData.lastActive = Date.now();
        gameState.players.set(ws, playerData);

        sendGameState(ws);
    } else {
        handleError(ws, new Error('Invalid reconnection attempt'));
    }
}

// Sync game state with client
function syncGameState(ws) {
    sendGameState(ws);
}

// Get public game state (excluding sensitive data)
function getPublicGameState() {
    return {
        playerCount: gameState.players.size,
        currentRound: gameState.currentRound,
        mode: gameState.mode,
        isPlaying: gameState.isPlaying,
        players: Array.from(gameState.players.values()),
        lastUpdateTime: gameState.lastUpdateTime
    };
}

// Send game state to a specific client
function sendGameState(ws) {
    ws.send(JSON.stringify({
        type: 'gameState',
        data: getPublicGameState()
    }));
}

// Enhanced disconnect handling
function handleDisconnect(ws) {
    connections.delete(ws.id);

    if (gameState.players.has(ws)) {
        const player = gameState.players.get(ws);

        // Keep player data for potential reconnection
        setTimeout(() => {
            if (!Array.from(gameState.players.values())
                .find(p => p.id === player.id)) {
                gameState.players.delete(ws);
                broadcast({
                    type: 'playerLeft',
                    data: {
                        playerCount: gameState.players.size,
                        players: Array.from(gameState.players.values())
                    }
                });
            }
        }, CLIENT_TIMEOUT);
    }
}

// Enhanced broadcast with retry mechanism
function broadcast(message, excludeWs = null) {
    const messageStr = JSON.stringify(message);
    const failedClients = [];

    wss.clients.forEach(client => {
        if (client !== excludeWs && client.readyState === WebSocket.OPEN) {
            try {
                client.send(messageStr);
            } catch (error) {
                failedClients.push(client);
                console.error('Broadcast failed for client:', client.id);
            }
        }
    });

    // Retry failed broadcasts once
    if (failedClients.length > 0) {
        setTimeout(() => {
            failedClients.forEach(client => {
                if (client.readyState === WebSocket.OPEN) {
                    try {
                        client.send(messageStr);
                    } catch (error) {
                        console.error('Retry failed for client:', client.id);
                    }
                }
            });
        }, 1000);
    }
}

// Helper functions
function generatePlayerId() {
    return 'player_' + Math.random().toString(36).substr(2, 9);
}

function generateConnectionId() {
    return 'conn_' + Math.random().toString(36).substr(2, 9);
}

// Handle player answers
function handleAnswer(ws, data) {
    if (!gameState.isPlaying || !gameState.players.has(ws)) {
        return handleError(ws, new Error('Invalid answer submission'));
    }

    const player = gameState.players.get(ws);
    const roundIndex = gameState.currentRound - 1;

    // Validate answer submission
    if (roundIndex < 0 || roundIndex >= gameState.clips.length) {
        return handleError(ws, new Error('Invalid round'));
    }

    // Check if player already answered
    if (gameState.answers.some(a => a.playerId === player.id && a.round === roundIndex)) {
        return handleError(ws, new Error('Answer already submitted'));
    }

    // Record answer
    const answerData = {
        playerId: player.id,
        round: roundIndex,
        answer: data.answer,
        timestamp: Date.now()
    };
    gameState.answers.push(answerData);

    // Calculate score based on answer correctness and timing
    const isCorrect = data.answer === gameState.clips[roundIndex].correctAnswer;
    const timeBonus = Math.max(0, Math.floor(data.timeRemaining / 1000)); // Convert ms to seconds
    const points = isCorrect ? (100 + (timeBonus * 10)) : 0;

    // Update player score
    player.score += points;
    gameState.players.set(ws, player);

    // Notify player of answer receipt
    ws.send(JSON.stringify({
        type: 'answerReceived',
        data: {
            isCorrect,
            points,
            correctAnswer: gameState.clips[roundIndex].correctAnswer
        }
    }));

    // Check if all players have answered
    const answersThisRound = gameState.answers.filter(a => a.round === roundIndex);
    if (answersThisRound.length === gameState.players.size) {
        endRound();
    }
}

// Start a new game
function startGame(data) {
    if (gameState.isPlaying) {
        return;
    }

    // Validate game mode
    const validModes = ['easy', 'medium', 'hard', 'standard'];
    if (!validModes.includes(data.mode)) {
        throw new Error('Invalid game mode');
    }

    // Reset game state
    gameState.currentRound = 0;
    gameState.mode = data.mode;
    gameState.isPlaying = true;
    gameState.answers = [];
    gameState.clips = loadClips(data.mode);
    gameState.lastUpdateTime = Date.now();

    // Reset player scores
    for (const [ws, player] of gameState.players.entries()) {
        player.score = 0;
        gameState.players.set(ws, player);
    }

    // Broadcast game start
    broadcast({
        type: 'gameStarted',
        data: {
            mode: data.mode,
            playerCount: gameState.players.size,
            maxRounds: gameState.maxRounds
        }
    });

    // Start first round after a short delay
    setTimeout(startNextRound, 3000);
}

// End the current game
function endGame() {
    if (!gameState.isPlaying) {
        return;
    }

    // Calculate final standings
    const players = Array.from(gameState.players.values())
        .sort((a, b) => b.score - a.score)
        .map((player, index) => ({
            ...player,
            rank: index + 1
        }));

    const winner = players[0];

    // Broadcast game over
    broadcast({
        type: 'gameOver',
        data: {
            winner,
            players,
            finalScores: players.map(p => ({
                id: p.id,
                name: p.name,
                score: p.score,
                rank: p.rank
            }))
        }
    });

    // Reset game state
    gameState.isPlaying = false;
    gameState.currentRound = 0;
    gameState.mode = null;
    gameState.answers = [];
    gameState.lastUpdateTime = Date.now();
}

// Helper function to start next round
function startNextRound() {
    if (!gameState.isPlaying) {
        return;
    }

    gameState.currentRound++;

    // Check if game should end
    if (gameState.currentRound > gameState.maxRounds) {
        return endGame();
    }

    const roundIndex = gameState.currentRound - 1;
    const clip = gameState.clips[roundIndex];

    // Broadcast round start
    broadcast({
        type: 'roundStart',
        data: {
            round: gameState.currentRound,
            maxRounds: gameState.maxRounds,
            clipUrl: clip.url,
            question: clip.question,
            options: clip.options,
            timeLimit: getRoundTimeLimit(gameState.mode)
        }
    });
}

// Helper function to end current round
function endRound() {
    const roundIndex = gameState.currentRound - 1;
    const roundAnswers = gameState.answers.filter(a => a.round === roundIndex);
    const correctAnswer = gameState.clips[roundIndex].correctAnswer;

    // Calculate round results
    const results = Array.from(gameState.players.values()).map(player => {
        const answer = roundAnswers.find(a => a.playerId === player.id);
        return {
            playerId: player.id,
            name: player.name,
            answer: answer ? answer.answer : null,
            isCorrect: answer ? answer.answer === correctAnswer : false,
            score: player.score
        };
    });

    // Broadcast round results
    broadcast({
        type: 'roundEnd',
        data: {
            round: gameState.currentRound,
            correctAnswer,
            results
        }
    });

    // Start next round after delay
    setTimeout(startNextRound, 5000);
}

// Helper function to get round time limit based on game mode
function getRoundTimeLimit(mode) {
    const timeLimits = {
        easy: 30000,    // 30 seconds
        medium: 20000,  // 20 seconds
        hard: 15000,    // 15 seconds
        standard: 25000 // 25 seconds
    };
    return timeLimits[mode] || timeLimits.standard;
}

// Helper function to load clips based on game mode
function loadClips(mode) {
    // YouTube clips with start times for 3-second segments
    const youtubeClips = [
        {
            videoId: 'dQw4w9WgXcQ', // Rick Astley - Never Gonna Give You Up
            startTime: 43,
            question: 'What is the singer doing in this clip?',
            correctAnswer: 'Dancing',
            wrongAnswers: ['Singing only', 'Playing guitar', 'Standing still']
        },
        {
            videoId: 'kJQP7kiw5Fk', // Luis Fonsi - Despacito
            startTime: 60,
            question: 'What language is being sung?',
            correctAnswer: 'Spanish',
            wrongAnswers: ['English', 'Portuguese', 'Italian']
        },
        {
            videoId: 'fJ9rUzIMcZQ', // Queen - Bohemian Rhapsody
            startTime: 180,
            question: 'What instrument is prominently featured?',
            correctAnswer: 'Piano',
            wrongAnswers: ['Guitar', 'Drums', 'Violin']
        },
        {
            videoId: 'hTWKbfoikeg', // Smash Mouth - All Star
            startTime: 25,
            question: 'What genre is this music?',
            correctAnswer: 'Rock',
            wrongAnswers: ['Pop', 'Jazz', 'Classical']
        },
        {
            videoId: 'ZbZSe6N_BXs', // Pharrell Williams - Happy
            startTime: 45,
            question: 'What is the mood of this song?',
            correctAnswer: 'Happy',
            wrongAnswers: ['Sad', 'Angry', 'Mysterious']
        },
        {
            videoId: 'L_jWHffIx5E', // Smash Mouth - All Star (different part)
            startTime: 60,
            question: 'What decade is this song from?',
            correctAnswer: '1990s',
            wrongAnswers: ['1980s', '2000s', '2010s']
        },
        {
            videoId: 'y6120QOlsfU', // Darude - Sandstorm
            startTime: 30,
            question: 'What type of music is this?',
            correctAnswer: 'Electronic',
            wrongAnswers: ['Rock', 'Classical', 'Country']
        },
        {
            videoId: 'djV11Xbc914', // Take On Me - a-ha
            startTime: 50,
            question: 'What decade is this song from?',
            correctAnswer: '1980s',
            wrongAnswers: ['1970s', '1990s', '2000s']
        },
        {
            videoId: 'SQoA_wjmE9w', // Thriller - Michael Jackson
            startTime: 120,
            question: 'Who is the artist?',
            correctAnswer: 'Michael Jackson',
            wrongAnswers: ['Prince', 'Elvis Presley', 'James Brown']
        },
        {
            videoId: 'rY0WxgSXdEE', // Earth, Wind & Fire - September
            startTime: 40,
            question: 'What month is mentioned in the song?',
            correctAnswer: 'September',
            wrongAnswers: ['October', 'November', 'December']
        }
    ];

    // Select clips for this game
    const selectedClips = youtubeClips.slice(0, gameState.maxRounds);

    return selectedClips.map(clip => ({
        videoId: clip.videoId,
        startTime: clip.startTime,
        duration: 3, // 3 seconds
        url: `https://www.youtube.com/embed/${clip.videoId}?start=${clip.startTime}&end=${clip.startTime + 3}&autoplay=1&controls=0&modestbranding=1&rel=0`,
        question: clip.question,
        correctAnswer: clip.correctAnswer,
        options: generateOptions(mode, clip.correctAnswer, clip.wrongAnswers)
    }));
}

// Helper function to generate answer options
function generateOptions(mode, correctAnswer, wrongAnswers = []) {
    const optionCounts = {
        easy: 4,
        medium: 6,
        hard: 8,
        standard: 4
    };

    const count = optionCounts[mode] || optionCounts.standard;
    const options = [correctAnswer];

    // Add predefined wrong answers first
    wrongAnswers.forEach(wrongAnswer => {
        if (options.length < count && !options.includes(wrongAnswer)) {
            options.push(wrongAnswer);
        }
    });

    // Generate additional wrong options if needed
    const additionalWrongAnswers = [
        'Option A', 'Option B', 'Option C', 'Option D', 'Option E', 'Option F',
        'Choice 1', 'Choice 2', 'Choice 3', 'Choice 4', 'Choice 5', 'Choice 6',
        'Answer X', 'Answer Y', 'Answer Z', 'Alternative 1', 'Alternative 2'
    ];

    let wrongIndex = 0;
    while (options.length < count && wrongIndex < additionalWrongAnswers.length) {
        const wrongOption = additionalWrongAnswers[wrongIndex];
        if (!options.includes(wrongOption)) {
            options.push(wrongOption);
        }
        wrongIndex++;
    }

    // Shuffle options
    return options.sort(() => Math.random() - 0.5);
}

// Start the server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});