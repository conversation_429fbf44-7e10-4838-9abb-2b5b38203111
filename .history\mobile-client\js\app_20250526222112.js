// Mobile Client - Main JavaScript

// Initialize variables
let socket;
let playerId;
let playerName;
let playerAvatar = 1;
let currentQuestion;
let currentOptions;
let selectedAnswer;
let score = 0;
let gameMode;

// DOM Elements
const registrationScreen = document.getElementById('registration-screen');
const waitingScreen = document.getElementById('waiting-screen');
const questionScreen = document.getElementById('question-screen');
const answerSubmittedScreen = document.getElementById('answer-submitted-screen');
const roundResultScreen = document.getElementById('round-result-screen');
const gameOverScreen = document.getElementById('game-over-screen');

const playerNameInput = document.getElementById('player-name');
const avatarSelections = document.querySelectorAll('.avatar');
const joinGameButton = document.getElementById('join-game');
const playerAvatarDisplay = document.getElementById('player-avatar');
const displayNameElement = document.getElementById('display-name');
const optionsContainer = document.getElementById('options-container');
const resultMessageElement = document.getElementById('result-message');
const correctAnswerTextElement = document.getElementById('correct-answer-text');
const yourAnswerTextElement = document.getElementById('your-answer-text');
const pointsEarnedElement = document.getElementById('points-earned');
const finalScoreElement = document.getElementById('final-score');
const finalRankElement = document.getElementById('final-rank');
const winnerMessageElement = document.getElementById('winner-message');
const playAgainButton = document.getElementById('play-again-btn');

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
    setupEventListeners();
});

// Initialize the application
function initializeApp() {
    // Connect to Socket.IO server
    connectToServer();
}

// Connect to the Socket.IO server
function connectToServer() {
    // Connect directly to game server WebSocket endpoint
    const wsUrl = 'ws://localhost:3000/ws';
    socket = new WebSocket(wsUrl);
    
    // Setup socket event listeners
    setupSocketListeners();
}

// Setup WebSocket event listeners
function setupSocketListeners() {
    socket.onopen = () => {
        console.log('Connected to server');
    };
    
    socket.onclose = () => {
        console.log('Disconnected from server');
        // Try to reconnect after a delay
        setTimeout(connectToServer, 5000);
    };
    
    socket.onerror = (error) => {
        console.error('WebSocket error:', error);
    };
    
    socket.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data);
            handleServerMessage(data);
        } catch (error) {
            console.error('Error handling message:', error);
        }
    };
}

// Handle server messages
function handleServerMessage(data) {
    switch(data.type) {
        case 'game_started':
            handleGameStarted(data.data);
            break;
        case 'new_round':
            handleNewRound(data.data);
            break;
        case 'round_ended':
            handleRoundEnded(data.data);
            break;
        case 'game_over':
            handleGameOver(data.data);
            break;
        case 'game_reset':
            handleGameReset();
            break;
    }
}

// Send message to server
function sendToServer(type, data) {
    if (socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ type, data }));
    }
}

// Setup event listeners
function setupEventListeners() {
    // Avatar selection
    avatarSelections.forEach(avatar => {
        avatar.addEventListener('click', () => {
            // Remove selected class from all avatars
            avatarSelections.forEach(a => a.classList.remove('selected'));
            
            // Add selected class to clicked avatar
            avatar.classList.add('selected');
            
            // Set player avatar
            playerAvatar = parseInt(avatar.dataset.avatar);
        });
    });
    
    // Join game button
    joinGameButton.addEventListener('click', joinGame);
    
    // Play again button
    playAgainButton.addEventListener('click', () => {
        // Show waiting screen
        showScreen(waitingScreen);
    });
}

// Join the game
function joinGame() {
    // Get player name
    playerName = playerNameInput.value.trim();
    
    // Validate player name
    if (!playerName) {
        alert('Please enter your name');
        return;
    }
    
    // Generate player ID
    playerId = 'player_' + Date.now();
    
    // Update player info display
    displayNameElement.textContent = playerName;
    playerAvatarDisplay.style.backgroundColor = getAvatarColor(playerAvatar);
    
    // Send player info to server
    sendToServer('register', {
        name: playerName,
        avatar: playerAvatar
    });
    
    // Show waiting screen
    showScreen(waitingScreen);
}

// Populate options
function populateOptions(options) {
    // Clear options container
    optionsContainer.innerHTML = '';
    
    // Add hard-mode class for many options
    if (options.length > 10) {
        optionsContainer.className = 'hard-mode';
    } else {
        optionsContainer.className = '';
    }
    
    // Add each option to the container
    options.forEach((option, index) => {
        const optionButton = document.createElement('div');
        optionButton.className = 'option-btn';
        optionButton.textContent = option;
        
        // Add click event listener
        optionButton.addEventListener('click', () => {
            // Select this option
            selectOption(optionButton, option);
        });
        
        // Add option to container
        optionsContainer.appendChild(optionButton);
    });
}

// Select an option
function selectOption(optionElement, optionText) {
    // Remove selected class from all options
    const optionElements = document.querySelectorAll('.option-btn');
    optionElements.forEach(element => {
        element.classList.remove('selected');
    });
    
    // Add selected class to clicked option
    optionElement.classList.add('selected');
    
    // Set selected answer
    selectedAnswer = optionText;
    
    // Send answer to server
    socket.send(JSON.stringify({
        type: 'player_answer',
        data: {
            playerId: playerId,
            answer: optionText,
            timestamp: Date.now()
        }
    }));
    
    // Show answer submitted screen
    showScreen(answerSubmittedScreen);
}

// Start timer animation
function startTimerAnimation() {
    // Reset timer bar
    const timerBar = document.querySelector('.timer-bar');
    
    // Remove and reapply animation to restart it
    timerBar.style.animation = 'none';
    timerBar.offsetHeight; // Trigger reflow
    timerBar.style.animation = 'timer 5s linear forwards';
    
    // Auto-submit after timer ends
    setTimeout(() => {
        if (questionScreen.classList.contains('active') && !selectedAnswer) {
            // If still on question screen and no answer selected, auto-submit
            socket.send(JSON.stringify({
                type: 'player_answer',
                data: {
                    playerId: playerId,
                    answer: null,
                    timestamp: Date.now()
                }
            }));
            
            showScreen(answerSubmittedScreen);
        }
    }, 5000);
}

// Get avatar color based on avatar ID
function getAvatarColor(avatarId) {
    const colors = [
        '#f44336', // Red
        '#4caf50', // Green
        '#2196f3', // Blue
        '#9c27b0', // Purple
        '#ff9800', // Orange
        '#03a9f4'  // Light Blue
    ];
    
    return colors[(avatarId - 1) % colors.length];
}

// Show a specific screen
function showScreen(screen) {
    // Hide all screens
    registrationScreen.classList.remove('active');
    waitingScreen.classList.remove('active');
    questionScreen.classList.remove('active');
    answerSubmittedScreen.classList.remove('active');
    roundResultScreen.classList.remove('active');
    gameOverScreen.classList.remove('active');
    
    // Show specified screen
    screen.classList.add('active');
}

function showQRCode() {
    const qrEl = document.getElementById('qrCode');
    qrEl.innerHTML = '';
    // Use game server URL for joining
    const joinUrl = 'http://localhost:3000/mobile';
    new QRCode(qrEl, { text: joinUrl, width: 200, height: 200 });
    document.querySelector('.qr-container').classList.add('active');
} 