class GameClient {
    constructor() {
        this.socket = null;
        this.gameData = null;
        this.playerData = null;
        this.currentRound = null;
        this.selectedAnswer = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.playerId = null;
        this.lastGameState = null;
        this.isConnecting = false;
    }

    async connect(gameData) {
        if (this.isConnecting) return;
        this.isConnecting = true;
        
        try {
            this.gameData = gameData;
            await this.establishConnection();
        } catch (error) {
            console.error('Connection failed:', error);
            UI.showError('Failed to connect to the game server');
        } finally {
            this.isConnecting = false;
        }
    }

    async establishConnection() {
        return new Promise((resolve, reject) => {
            try {
                this.socket = new WebSocket(`ws://${this.gameData.serverUrl}`);
                this.setupSocketListeners(resolve, reject);
            } catch (error) {
                reject(error);
            }
        });
    }

    setupSocketListeners(resolve, reject) {
        this.socket.onopen = () => {
            console.log('Connected to game server');
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            
            if (this.playerId) {
                this.attemptReconnection();
            } else {
                UI.showScreen('registration-screen');
            }
            
            resolve();
        };

        this.socket.onclose = () => {
            console.log('Disconnected from game server');
            this.handleDisconnection();
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket error:', error);
            reject(error);
        };

        this.socket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
            } catch (error) {
                console.error('Error handling message:', error);
            }
        };
    }

    startHeartbeat() {
        // Send ping every 20 seconds
        this.heartbeatInterval = setInterval(() => {
            if (this.socket.readyState === WebSocket.OPEN) {
                this.sendMessage('ping', {});
            }
        }, 20000);
    }

    handleDisconnection() {
        clearInterval(this.heartbeatInterval);
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            UI.showMessage('Connection lost. Attempting to reconnect...');
            setTimeout(() => this.reconnect(), this.getReconnectDelay());
        } else {
            UI.showError('Connection lost. Please refresh to reconnect.');
        }
    }

    getReconnectDelay() {
        // Exponential backoff
        return Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    }

    async reconnect() {
        this.reconnectAttempts++;
        try {
            await this.connect(this.gameData);
        } catch (error) {
            console.error('Reconnection failed:', error);
        }
    }

    attemptReconnection() {
        this.sendMessage('reconnect', {
            playerId: this.playerId,
            lastUpdateTime: this.lastGameState?.lastUpdateTime
        });
    }

    handleMessage(message) {
        switch (message.type) {
            case 'gameState':
                this.handleGameState(message.data);
                break;
            case 'registered':
                this.handleRegistration(message.data);
                break;
            case 'roundStart':
                this.handleRoundStart(message.data);
                break;
            case 'roundEnd':
                this.handleRoundEnd(message.data);
                break;
            case 'gameOver':
                this.handleGameOver(message.data);
                break;
            case 'error':
                this.handleError(message.data);
                break;
            case 'pong':
                // Heartbeat response
                break;
        }
    }

    sendMessage(type, data) {
        if (this.socket.readyState === WebSocket.OPEN) {
            try {
                this.socket.send(JSON.stringify({ type, data }));
            } catch (error) {
                console.error('Error sending message:', error);
            }
        }
    }

    registerPlayer(name, avatarId) {
        this.playerData = { name, avatarId };
        this.sendMessage('register', this.playerData);
    }

    handleRegistration(data) {
        this.playerId = data.playerId;
        this.lastGameState = data.gameState;
        UI.showScreen('game-screen');
        UI.updatePlayerInfo(this.playerData);
    }

    submitAnswer(answer) {
        if (!this.currentRound || this.selectedAnswer) return;
        
        this.selectedAnswer = answer;
        this.sendMessage('answer', {
            roundId: this.currentRound.id,
            answer: answer,
            timestamp: Date.now()
        });
    }

    handleGameState(state) {
        this.lastGameState = state;
        
        switch (state.phase) {
            case 'waiting':
                UI.showScreen('game-screen');
                UI.showWaitingMessage();
                break;
            case 'playing':
                this.currentRound = state.currentRound;
                UI.updateGameScreen(state);
                break;
            case 'results':
                UI.showScreen('results-screen');
                UI.updateResults(state.results);
                break;
        }
    }

    handleRoundStart(roundData) {
        this.currentRound = roundData;
        this.selectedAnswer = null;
        UI.showScreen('game-screen');
        UI.setupAnswerOptions(roundData.options);
        UI.startTimer(roundData.timeLimit);
    }

    handleRoundEnd(results) {
        UI.showScreen('results-screen');
        UI.updateResults(results);
    }

    handleGameOver(finalResults) {
        UI.showScreen('results-screen');
        UI.showGameOver(finalResults);
    }

    handleError(error) {
        UI.showError(error.message);
    }
}

// Initialize Game Client
document.addEventListener('DOMContentLoaded', () => {
    const gameClient = new GameClient();
    window.gameClient = gameClient; // Make it accessible to other modules
}); 