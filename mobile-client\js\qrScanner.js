class QRScanner {
    constructor() {
        this.html5QrCode = null;
        this.onSuccess = null;
    }

    init(onSuccess) {
        this.onSuccess = onSuccess;
        const config = {
            fps: 10,
            qrbox: { width: 250, height: 250 },
            aspectRatio: 1.0
        };

        this.html5QrCode = new Html5Qrcode("qr-reader");
        
        this.html5QrCode.start(
            { facingMode: "environment" },
            config,
            this.handleQRCodeSuccess.bind(this),
            this.handleQRCodeError.bind(this)
        );
    }

    handleQRCodeSuccess(decodedText) {
        try {
            const gameData = JSON.parse(decodedText);
            if (gameData.gameId && gameData.serverUrl) {
                this.stop();
                if (this.onSuccess) {
                    this.onSuccess(gameData);
                }
            }
        } catch (error) {
            console.error('Invalid QR code format:', error);
        }
    }

    handleQRCodeError(error) {
        // Suppress continuous error logging
        // console.error('QR Code scanning error:', error);
    }

    stop() {
        if (this.html5QrCode && this.html5QrCode.isScanning) {
            this.html5QrCode.stop().catch(error => {
                console.error('Error stopping QR scanner:', error);
            });
        }
    }
}

// Initialize QR Scanner
document.addEventListener('DOMContentLoaded', () => {
    const qrScanner = new QRScanner();
    window.qrScanner = qrScanner; // Make it accessible to other modules
}); 