:root {
    --primary-color: #2196F3;
    --secondary-color: #FF4081;
    --background-color: #F5F5F5;
    --text-color: #333333;
    --error-color: #F44336;
    --success-color: #4CAF50;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

#app {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}

.screen {
    display: none;
    padding: 20px;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.screen.active {
    display: block;
}

h2 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color);
}

/* QR Scanner Styles */
#qr-reader {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

/* Registration Screen */
#registration-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

#player-name {
    padding: 12px;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    font-size: 16px;
}

#avatar-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 20px 0;
}

.avatar-option {
    aspect-ratio: 1;
    border: 2px solid transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s;
}

.avatar-option.selected {
    border-color: var(--primary-color);
    transform: scale(1.1);
}

/* Game Screen */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

#timer {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

#answer-options {
    display: grid;
    gap: 10px;
    margin: 20px 0;
}

.answer-option {
    padding: 15px;
    background: white;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.answer-option:hover {
    background: var(--primary-color);
    color: white;
}

.answer-option.selected {
    background: var(--primary-color);
    color: white;
}

/* Results Screen */
#leaderboard {
    margin-top: 20px;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

/* Buttons */
button {
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.2s;
}

button:hover {
    background: darken(var(--primary-color), 10%);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.screen.active {
    animation: fadeIn 0.3s ease-in-out;
} 