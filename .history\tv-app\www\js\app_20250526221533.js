document.addEventListener('deviceready', onDeviceReady, false);
// Fallback for browser testing
document.addEventListener('DOMContentLoaded', onDeviceReady, false);

let currentFocusIndex = 0;
const menuItems = [];
let wsConnection = null;
const SERVER_URL = 'ws://localhost:3000/ws';
let gameState = {
    mode: null,
    currentRound: 0,
    maxRounds: 10,
    players: new Map(),
    isPlaying: false,
    currentAnswers: new Map()
};
let videoPlayer = null;

// Game mode configurations
const GAME_MODES = {
    easy: {
        optionCount: 4,
        timeLimit: 20,
        pointsBase: 100,
        pointsTimeBonus: 5
    },
    medium: {
        optionCount: 10,
        timeLimit: 15,
        pointsBase: 200,
        pointsTimeBonus: 10
    },
    hard: {
        optionCount: 20,
        timeLimit: 10,
        pointsBase: 300,
        pointsTimeBonus: 15
    },
    standard: {
        optionCount: 4,
        timeLimit: 15,
        pointsBase: 100,
        pointsTimeBonus: 10
    }
};

// Timer and round management
let roundTimer = null;
let answerTimer = null;

function onDeviceReady() {
    setupDPadNavigation();
    connectToServer();
    setupVideoPlayer();
    
    // Hide cordova splash screen
    if (navigator.splashscreen) {
        navigator.splashscreen.hide();
    }
}

function setupDPadNavigation() {
    // Get all menu items and make first one focused
    menuItems.push(...document.querySelectorAll('.menu-item'));
    if (menuItems.length > 0) {
        menuItems[0].classList.add('focused');
    }

    // Handle keydown events for D-pad navigation
    document.addEventListener('keydown', (event) => {
        switch(event.key) {
            case 'ArrowUp':
                moveFocus('up');
                break;
            case 'ArrowDown':
                moveFocus('down');
                break;
            case 'Enter':
                selectMenuItem();
                break;
        }
    });
}

function moveFocus(direction) {
    // Remove focus from current item
    menuItems[currentFocusIndex].classList.remove('focused');

    // Update focus index based on direction
    if (direction === 'up') {
        currentFocusIndex = (currentFocusIndex - 1 + menuItems.length) % menuItems.length;
    } else if (direction === 'down') {
        currentFocusIndex = (currentFocusIndex + 1) % menuItems.length;
    }

    // Add focus to new item
    menuItems[currentFocusIndex].classList.add('focused');
    menuItems[currentFocusIndex].focus();
}

function selectMenuItem() {
    const selectedItem = menuItems[currentFocusIndex];
    const gameMode = selectedItem.dataset.mode;

    // Remove selection from all items
    menuItems.forEach(item => item.classList.remove('selected'));
    
    // Add selection to current item
    selectedItem.classList.add('selected');

    // Send game mode to server
    wsConnection.send(JSON.stringify({
        type: 'startGame',
        data: {
            mode: gameMode
        }
    }));

    // Show QR code
    showQRCode();
}

function connectToServer() {
    wsConnection = new WebSocket(SERVER_URL);

    wsConnection.onopen = () => {
        console.log('Connected to server');
        // Register as TV client
        wsConnection.send(JSON.stringify({
            type: 'register',
            data: {
                clientType: 'tv'
            }
        }));
    };

    wsConnection.onmessage = (event) => {
        handleServerMessage(JSON.parse(event.data));
    };

    wsConnection.onclose = () => {
        console.log('Disconnected from server');
        // Try to reconnect after a delay
        setTimeout(connectToServer, 5000);
    };

    wsConnection.onerror = (error) => {
        console.error('WebSocket error:', error);
    };
}

function handleServerMessage(message) {
    switch(message.type) {
        case 'playerJoined':
            updatePlayerCount(message.data.playerCount);
            updateLeaderboard(message.data.players);
            break;
        case 'playerLeft':
            updatePlayerCount(message.data.playerCount);
            updateLeaderboard(message.data.players);
            break;
        case 'gameStarted':
            startGame(message.data);
            break;
        case 'roundStart':
            startRound(message.data);
            break;
        case 'scoreUpdate':
            updateLeaderboard(message.data.players);
            break;
        case 'gameOver':
            endGame(message.data);
            break;
        case 'playerAnswer':
            handlePlayerAnswer(
                message.data.playerId,
                message.data.answer,
                message.data.timeRemaining
            );
            break;
    }
}

function updatePlayerCount(count) {
    const playerCount = document.getElementById('playerCount');
    playerCount.textContent = count;
}

function updateLeaderboard(players) {
    const leaderboardList = document.getElementById('leaderboardList');
    leaderboardList.innerHTML = '';

    // Sort players by score
    players.sort((a, b) => b.score - a.score);

    // Create leaderboard items
    players.forEach(player => {
        const li = document.createElement('li');
        li.className = 'leaderboard-item';
        li.innerHTML = `
            <span class="player-name">${player.name}</span>
            <span class="player-score">${player.score}</span>
        `;
        leaderboardList.appendChild(li);
    });
}

function startGame(data) {
    document.querySelector('.qr-container').classList.remove('active');
    document.querySelector('.game-screen').classList.add('active');
}

function startRound(data) {
    gameState.currentRound++;
    
    // Update round display
    document.getElementById('currentRound').textContent = gameState.currentRound;
    
    // Reset answer collection
    gameState.currentAnswers = new Map();
    
    // Start video clip
    videoPlayer.playClip(data.clipIndex);
    
    // Broadcast round start to players
    broadcastToPlayers({
        type: 'roundStart',
        data: {
            roundNumber: gameState.currentRound,
            totalRounds: gameState.maxRounds
        }
    });
}

function endGame(data) {
    document.querySelector('.game-screen').classList.remove('active');
    document.getElementById('winner-message').textContent = 
        `Winner: ${data.players[0].name} with ${data.players[0].score} points!`;
    document.getElementById('game-over-screen').classList.add('active');
}

function setupVideoPlayer() {
    videoPlayer = new VideoPlayer();
    videoPlayer.setOnEndCallback(handleVideoEnd);

    // Load sample clips (replace with actual clips)
    const sampleClips = [
        '/clips/sample1.mp4',
        '/clips/sample2.mp4',
        '/clips/sample3.mp4'
    ];
    videoPlayer.loadClips(sampleClips);
}

function handleVideoEnd(clipIndex) {
    // Show answer options to players
    broadcastToPlayers({
        type: 'showAnswers',
        data: {
            options: generateAnswerOptions(clipIndex)
        }
    });

    // Start answer timer
    startAnswerPhase(clipIndex);
}

function calculateScore(timeRemaining, gameMode) {
    const mode = GAME_MODES[gameMode];
    return mode.pointsBase + (timeRemaining * mode.pointsTimeBonus);
}

function generateAnswerOptions(clipIndex) {
    const clip = videoPlayer.getClipInfo(clipIndex);
    const correctAnswer = clip.answer;
    const mode = gameState.mode;
    
    // Get number of options based on game mode
    const optionCount = GAME_MODES[mode].optionCount;
    
    // Generate wrong options
    const wrongOptions = generateWrongOptions(optionCount - 1, correctAnswer);
    
    // Combine correct answer with wrong options
    const options = [correctAnswer, ...wrongOptions];
    
    // Shuffle options
    return options.sort(() => Math.random() - 0.5);
}

function generateWrongOptions(count, correctAnswer) {
    // This would typically pull from a database of possible answers
    // For now, generate dummy wrong answers
    const wrongOptions = [];
    for (let i = 0; i < count; i++) {
        wrongOptions.push(`Wrong Answer ${i + 1}`);
    }
    return wrongOptions;
}

function startAnswerPhase(clipIndex) {
    const answerData = generateAnswerOptions(clipIndex);
    
    // Send answer options to players
    broadcastToPlayers({
        type: 'showAnswers',
        data: {
            options: answerData,
            timeLimit: GAME_MODES[gameState.mode].timeLimit
        }
    });
    
    // Start answer timer
    startAnswerTimer(GAME_MODES[gameState.mode].timeLimit);
}

function startAnswerTimer(timeLimit) {
    let timeRemaining = timeLimit;
    
    // Update timer display
    const timerDisplay = document.getElementById('timer');
    timerDisplay.textContent = timeRemaining;
    
    // Clear any existing timer
    if (answerTimer) {
        clearInterval(answerTimer);
    }
    
    // Start new timer
    answerTimer = setInterval(() => {
        timeRemaining--;
        timerDisplay.textContent = timeRemaining;
        
        // Broadcast time update to players
        broadcastToPlayers({
            type: 'timerUpdate',
            data: { timeRemaining }
        });
        
        if (timeRemaining <= 0) {
            endAnswerPhase();
        }
    }, 1000);
}

function endAnswerPhase() {
    // Clear timer
    if (answerTimer) {
        clearInterval(answerTimer);
        answerTimer = null;
    }
    
    // Calculate and update scores
    updateScores(Array.from(gameState.currentAnswers.values()));
    
    // Show correct answer
    const correctAnswer = videoPlayer.getCurrentClipAnswer();
    broadcastToPlayers({
        type: 'revealAnswer',
        data: { correctAnswer }
    });
    
    // Check if game should end
    if (gameState.currentRound >= gameState.maxRounds) {
        endGame({
            players: Array.from(gameState.players.values())
        });
    } else {
        // Schedule next round
        setTimeout(startNextRound, 5000);
    }
}

function handlePlayerAnswer(playerId, answer, timeRemaining) {
    // Only accept first answer from each player
    if (!gameState.currentAnswers.has(playerId)) {
        gameState.currentAnswers.set(playerId, {
            playerId,
            answer,
            timeRemaining
        });
    }
}

function startNextRound() {
    if (gameState.currentRound < gameState.maxRounds) {
        gameState.currentRound++;
        videoPlayer.playClip(gameState.currentRound - 1);
    } else {
        endGame();
    }
}

function broadcastToPlayers(message) {
    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(message));
    }
}

function showQRCode() {
    const qrEl = document.getElementById('qrCode');
    qrEl.innerHTML = '';
    const joinUrl = `${window.location.origin}/mobile`;
    new QRCode(qrEl, { text: joinUrl, width: 200, height: 200 });
    document.querySelector('.qr-container').classList.add('active');
}

function updateScores(playerAnswers) {
    const mode = GAME_MODES[gameState.mode];
    const correctAnswer = videoPlayer.getCurrentClipAnswer();
    
    playerAnswers.forEach(answer => {
        const player = gameState.players.get(answer.playerId);
        if (player) {
            if (answer.answer === correctAnswer) {
                const score = calculateScore(answer.timeRemaining, gameState.mode);
                player.score += score;
            }
        }
    });
    
    // Update leaderboard with new scores
    updateLeaderboard(Array.from(gameState.players.values()));
} 