class VideoPlayer {
    constructor() {
        this.videoElement = document.getElementById('clipPlayer');
        this.currentClip = null;
        this.onEndCallback = null;
        this.clips = [];
        this.clipIndex = 0;

        // Bind event listeners
        this.videoElement.addEventListener('ended', () => this.handleVideoEnd());
        this.videoElement.addEventListener('error', (e) => this.handleError(e));
    }

    loadClips(clipUrls) {
        this.clips = clipUrls;
        this.clipIndex = 0;
    }

    async playClip(index = null) {
        if (index !== null) {
            this.clipIndex = index;
        }

        if (this.clipIndex >= this.clips.length) {
            console.error('No more clips to play');
            return false;
        }

        try {
            const clipUrl = this.clips[this.clipIndex];
            this.videoElement.src = clipUrl;
            await this.videoElement.play();
            return true;
        } catch (error) {
            console.error('Error playing video:', error);
            return false;
        }
    }

    pause() {
        this.videoElement.pause();
    }

    resume() {
        this.videoElement.play();
    }

    stop() {
        this.videoElement.pause();
        this.videoElement.currentTime = 0;
    }

    setOnEndCallback(callback) {
        this.onEndCallback = callback;
    }

    handleVideoEnd() {
        if (this.onEndCallback) {
            this.onEndCallback(this.clipIndex);
        }
    }

    handleError(error) {
        console.error('Video playback error:', error);
        // Implement error handling (e.g., show error message, try next clip, etc.)
    }

    getCurrentTime() {
        return this.videoElement.currentTime;
    }

    getDuration() {
        return this.videoElement.duration;
    }

    setVolume(volume) {
        this.videoElement.volume = Math.max(0, Math.min(1, volume));
    }

    mute() {
        this.videoElement.muted = true;
    }

    unmute() {
        this.videoElement.muted = false;
    }

    seekTo(time) {
        if (time >= 0 && time <= this.videoElement.duration) {
            this.videoElement.currentTime = time;
        }
    }

    isPlaying() {
        return !this.videoElement.paused;
    }

    getClipCount() {
        return this.clips.length;
    }

    getCurrentClipIndex() {
        return this.clipIndex;
    }
} 