<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Alyami Clips Quiz</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
        }

        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background-color: #0056b3;
        }

        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <h1>Alyami Clips Quiz - Test Page</h1>

    <div class="test-section">
        <h2>Server Status</h2>
        <div id="server-status" class="status warning">Checking server...</div>
        <button onclick="checkServer()">Check Server</button>
    </div>

    <div class="test-section">
        <h2>TV Application</h2>
        <p>This should show the TV interface with menu options:</p>
        <iframe src="http://localhost:3000/tv" title="TV Application"></iframe>
        <div class="status warning">
            <strong>Instructions:</strong>
            <ol>
                <li>Use arrow keys to navigate menu</li>
                <li>Press Enter to select a game mode</li>
                <li>QR code should appear for mobile connection</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>Mobile Client</h2>
        <p>This should show the mobile interface for players:</p>
        <iframe src="http://localhost:3000/mobile" title="Mobile Client"></iframe>
        <div class="status warning">
            <strong>Instructions:</strong>
            <ol>
                <li>Enter a player name</li>
                <li>Select an avatar</li>
                <li>Click "Join Game"</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>Testing Steps</h2>
        <ol>
            <li><strong>Start Server:</strong> Make sure the server is running on port 3000</li>
            <li><strong>Open TV App:</strong> Navigate the menu using arrow keys</li>
            <li><strong>Select Game Mode:</strong> Press Enter on a mode to start</li>
            <li><strong>Join as Player:</strong> Use the mobile client to join the game</li>
            <li><strong>Test Gameplay:</strong> Answer questions when they appear</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>YouTube Integration Test</h2>
        <p>Test a 3-second YouTube clip:</p>
        <iframe width="100%" height="200"
            src="https://www.youtube.com/embed/dQw4w9WgXcQ?start=43&end=46&autoplay=0&controls=1&modestbranding=1&rel=0"
            frameborder="0" allow="autoplay; encrypted-media"></iframe>
        <div class="status success">
            <strong>✓ YouTube Integration:</strong> The game now uses YouTube clips with 3-second segments!
        </div>
    </div>

    <div class="test-section">
        <h2>Features Added</h2>
        <div class="status success">
            <strong>✓ YouTube Clips:</strong> 10 pre-configured music video clips with questions
        </div>
        <div class="status success">
            <strong>✓ 3-Second Segments:</strong> Each clip plays for exactly 3 seconds
        </div>
        <div class="status success">
            <strong>✓ Smart Questions:</strong> Each clip has a relevant question and multiple choice answers
        </div>
        <div class="status warning">
            <strong>Local Network Only:</strong> This works only on localhost for testing
        </div>
    </div>

    <script>
        async function checkServer() {
            const statusDiv = document.getElementById('server-status');
            try {
                const response = await fetch('http://localhost:3000/tv');
                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✓ Server is running on port 3000';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '✗ Server responded with error: ' + response.status;
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '✗ Cannot connect to server. Make sure it\'s running on port 3000';
            }
        }

        // Check server status on page load
        checkServer();
    </script>
</body>

</html>