class UI {
    static screens = {
        qr: document.getElementById('qr-screen'),
        registration: document.getElementById('registration-screen'),
        game: document.getElementById('game-screen'),
        results: document.getElementById('results-screen')
    };

    static elements = {
        playerName: document.getElementById('player-name'),
        avatarGrid: document.getElementById('avatar-grid'),
        registrationForm: document.getElementById('registration-form'),
        playerInfo: document.getElementById('player-info'),
        timer: document.getElementById('timer'),
        answerOptions: document.getElementById('answer-options'),
        scoreDisplay: document.getElementById('score-display'),
        roundScore: document.getElementById('round-score'),
        leaderboard: document.getElementById('leaderboard')
    };

    static init() {
        this.setupEventListeners();
        this.loadAvatars();
        
        // Initialize QR scanner
        qrScanner.init((gameData) => {
            gameClient.connect(gameData);
        });
    }

    static setupEventListeners() {
        // Registration form submission
        this.elements.registrationForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const name = this.elements.playerName.value.trim();
            const selectedAvatar = this.elements.avatarGrid.querySelector('.avatar-option.selected');
            
            if (name && selectedAvatar) {
                gameClient.registerPlayer(name, selectedAvatar.dataset.avatarId);
            }
        });

        // Avatar selection
        this.elements.avatarGrid.addEventListener('click', (e) => {
            const avatar = e.target.closest('.avatar-option');
            if (avatar) {
                this.elements.avatarGrid.querySelectorAll('.avatar-option').forEach(a => {
                    a.classList.remove('selected');
                });
                avatar.classList.add('selected');
            }
        });
    }

    static loadAvatars() {
        // Load 6 default avatars
        const avatars = [
            'avatar1.png', 'avatar2.png', 'avatar3.png',
            'avatar4.png', 'avatar5.png', 'avatar6.png'
        ];

        this.elements.avatarGrid.innerHTML = avatars.map((avatar, index) => `
            <div class="avatar-option" data-avatar-id="${index + 1}">
                <img src="img/avatars/${avatar}" alt="Avatar ${index + 1}">
            </div>
        `).join('');
    }

    static showScreen(screenId) {
        Object.values(this.screens).forEach(screen => {
            screen.classList.remove('active');
        });
        this.screens[screenId].classList.add('active');
    }

    static updatePlayerInfo(playerData) {
        this.elements.playerInfo.innerHTML = `
            <img src="img/avatars/avatar${playerData.avatarId}.png" alt="Your avatar" class="player-avatar">
            <span>${playerData.name}</span>
        `;
    }

    static setupAnswerOptions(options) {
        this.elements.answerOptions.innerHTML = options.map((option, index) => `
            <div class="answer-option" data-answer="${index}">
                ${option}
            </div>
        `).join('');

        this.elements.answerOptions.querySelectorAll('.answer-option').forEach(option => {
            option.addEventListener('click', () => {
                if (!option.classList.contains('locked')) {
                    gameClient.submitAnswer(parseInt(option.dataset.answer));
                }
            });
        });
    }

    static startTimer(seconds) {
        let timeLeft = seconds;
        this.elements.timer.textContent = timeLeft;

        const timer = setInterval(() => {
            timeLeft--;
            this.elements.timer.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(timer);
            }
        }, 1000);
    }

    static lockAnswer(answerIndex) {
        this.elements.answerOptions.querySelectorAll('.answer-option').forEach(option => {
            option.classList.add('locked');
            if (parseInt(option.dataset.answer) === answerIndex) {
                option.classList.add('selected');
            }
        });
    }

    static updateResults(results) {
        // Update round score
        if (results.playerScore !== undefined) {
            this.elements.roundScore.innerHTML = `
                <h3>Your Score: ${results.playerScore}</h3>
                ${results.correct ? '<p class="correct">Correct!</p>' : '<p class="incorrect">Incorrect</p>'}
            `;
        }

        // Update leaderboard
        this.elements.leaderboard.innerHTML = results.leaderboard.map((player, index) => `
            <div class="leaderboard-item ${player.isCurrentPlayer ? 'current-player' : ''}">
                <span>${index + 1}. ${player.name}</span>
                <span>${player.score}</span>
            </div>
        `).join('');
    }

    static showGameOver(finalResults) {
        this.elements.roundScore.innerHTML = `
            <h3>Game Over!</h3>
            <p>Final Score: ${finalResults.playerScore}</p>
            <p>Final Rank: ${finalResults.rank} of ${finalResults.totalPlayers}</p>
        `;
    }

    static showError(message) {
        // Implement error toast or notification
        alert(message);
    }

    static showWaitingMessage() {
        this.elements.answerOptions.innerHTML = `
            <div class="waiting-message">
                <p>Waiting for next round...</p>
            </div>
        `;
    }
}

// Initialize UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    UI.init();
}); 