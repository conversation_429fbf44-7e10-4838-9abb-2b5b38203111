class VideoPlayer {
    constructor() {
        this.videoContainer = document.querySelector('.video-container');
        this.currentClip = null;
        this.onEndCallback = null;
        this.clips = [];
        this.clipIndex = 0;
        this.currentIframe = null;
        this.playbackTimer = null;
    }

    loadClips(clips) {
        this.clips = clips;
        this.clipIndex = 0;
    }

    async playClip(index = null) {
        if (index !== null) {
            this.clipIndex = index;
        }

        if (this.clipIndex >= this.clips.length) {
            console.error('No more clips to play');
            return false;
        }

        try {
            const clip = this.clips[this.clipIndex];
            this.currentClip = clip;

            // Clear any existing iframe
            if (this.currentIframe) {
                this.currentIframe.remove();
            }

            // Create YouTube iframe
            this.currentIframe = document.createElement('iframe');
            this.currentIframe.src = clip.url;
            this.currentIframe.width = '100%';
            this.currentIframe.height = '100%';
            this.currentIframe.frameBorder = '0';
            this.currentIframe.allow = 'autoplay; encrypted-media';
            this.currentIframe.style.border = 'none';

            // Clear container and add iframe
            this.videoContainer.innerHTML = '';
            this.videoContainer.appendChild(this.currentIframe);

            // Set timer for 3 seconds (YouTube autoplay + duration)
            if (this.playbackTimer) {
                clearTimeout(this.playbackTimer);
            }

            this.playbackTimer = setTimeout(() => {
                this.handleVideoEnd();
            }, 4000); // 3 seconds + 1 second buffer

            return true;
        } catch (error) {
            console.error('Error playing video:', error);
            return false;
        }
    }

    pause() {
        if (this.playbackTimer) {
            clearTimeout(this.playbackTimer);
        }
    }

    resume() {
        // YouTube iframe doesn't support resume, would need to reload
        this.playClip(this.clipIndex);
    }

    stop() {
        if (this.playbackTimer) {
            clearTimeout(this.playbackTimer);
        }
        if (this.currentIframe) {
            this.currentIframe.remove();
            this.currentIframe = null;
        }
    }

    setOnEndCallback(callback) {
        this.onEndCallback = callback;
    }

    handleVideoEnd() {
        if (this.onEndCallback) {
            this.onEndCallback(this.clipIndex);
        }
    }

    handleError(error) {
        console.error('Video playback error:', error);
        // Show error message in video container
        this.videoContainer.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; font-size: 18px;">
                <p>Video unavailable. Continuing with audio-only quiz...</p>
            </div>
        `;

        // Continue with the game after a short delay
        setTimeout(() => {
            this.handleVideoEnd();
        }, 2000);
    }

    getCurrentTime() {
        return 0; // YouTube iframe doesn't expose current time
    }

    getDuration() {
        return 3; // All clips are 3 seconds
    }

    setVolume(volume) {
        // YouTube iframe doesn't support volume control via JavaScript
    }

    mute() {
        // YouTube iframe doesn't support mute control via JavaScript
    }

    unmute() {
        // YouTube iframe doesn't support unmute control via JavaScript
    }

    seekTo(time) {
        // YouTube iframe doesn't support seeking via JavaScript
    }

    isPlaying() {
        return this.currentIframe !== null;
    }

    getClipCount() {
        return this.clips.length;
    }

    getCurrentClipIndex() {
        return this.clipIndex;
    }

    getClipInfo(index) {
        // Return dummy clip info for testing
        return {
            answer: `Correct Answer ${index + 1}`,
            url: this.clips[index] || '/clips/sample.mp4'
        };
    }

    getCurrentClipAnswer() {
        return `Correct Answer ${this.clipIndex + 1}`;
    }
}