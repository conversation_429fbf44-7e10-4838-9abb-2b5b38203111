/* TV App Styles */
:root {
    --primary-color: #2196F3;
    --secondary-color: #FFC107;
    --background-color: #000000;
    --text-color: #FFFFFF;
    --focus-color: #FF4081;
    --menu-item-height: 80px;
    --menu-spacing: 20px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: 'Roboto', sans-serif;
    padding: 40px;
    height: 100vh;
    overflow: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.header {
    text-align: center;
    margin-bottom: 60px;
}

.header h1 {
    font-size: 48px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.header p {
    font-size: 24px;
    color: var(--secondary-color);
}

.menu {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--menu-spacing);
}

.menu-item {
    width: 100%;
    max-width: 600px;
    height: var(--menu-item-height);
    background-color: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.menu-item:hover,
.menu-item:focus,
.menu-item.focused {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: var(--focus-color);
    transform: scale(1.05);
}

.menu-item.selected {
    background-color: var(--primary-color);
    border-color: var(--secondary-color);
}

.qr-container {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.qr-container.active {
    display: flex;
}

.qr-code {
    padding: 20px;
    background: white;
    border-radius: 10px;
    margin-bottom: 20px;
}

.qr-text {
    font-size: 24px;
    color: var(--text-color);
    text-align: center;
}

.game-screen {
    display: none;
    width: 100%;
    height: 100%;
}

.game-screen.active {
    display: block;
}

.video-container {
    width: 100%;
    height: 70vh;
    background-color: rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.leaderboard {
    position: fixed;
    right: 40px;
    top: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    min-width: 250px;
}

.leaderboard h2 {
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.leaderboard-list {
    list-style: none;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.player-name {
    margin-right: 20px;
}

.player-score {
    color: var(--primary-color);
}

/* Animation for focus transitions */
@keyframes focusAnimation {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.menu-item.focused {
    animation: focusAnimation 0.5s ease;
}

.game-info {
    text-align: center;
    margin-bottom: 20px;
    font-size: 18px;
}

.game-info p {
    margin: 5px 0;
}

#game-over-screen {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

#game-over-screen.active {
    display: flex;
}

#game-over-screen h2 {
    color: var(--secondary-color);
    margin-bottom: 20px;
    font-size: 36px;
}

#game-over-screen button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 18px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 20px;
}

#game-over-screen button:hover {
    background-color: var(--focus-color);
}