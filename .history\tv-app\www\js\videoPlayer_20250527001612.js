class VideoPlayer {
    constructor() {
        this.videoContainer = document.querySelector('.video-container');
        this.currentClip = null;
        this.onEndCallback = null;
        this.clips = [];
        this.clipIndex = 0;
        this.currentIframe = null;
        this.playbackTimer = null;
    }

    loadClips(clips) {
        this.clips = clips;
        this.clipIndex = 0;
    }

    async playClip(index = null) {
        if (index !== null) {
            this.clipIndex = index;
        }

        if (this.clipIndex >= this.clips.length) {
            console.error('No more clips to play');
            return false;
        }

        try {
            const clip = this.clips[this.clipIndex];
            this.currentClip = clip;

            // Clear any existing iframe
            if (this.currentIframe) {
                this.currentIframe.remove();
            }

            // Create YouTube iframe
            this.currentIframe = document.createElement('iframe');
            this.currentIframe.src = clip.url;
            this.currentIframe.width = '100%';
            this.currentIframe.height = '100%';
            this.currentIframe.frameBorder = '0';
            this.currentIframe.allow = 'autoplay; encrypted-media';
            this.currentIframe.style.border = 'none';

            // Clear container and add iframe
            this.videoContainer.innerHTML = '';
            this.videoContainer.appendChild(this.currentIframe);

            // Set timer for 3 seconds (YouTube autoplay + duration)
            if (this.playbackTimer) {
                clearTimeout(this.playbackTimer);
            }

            this.playbackTimer = setTimeout(() => {
                this.handleVideoEnd();
            }, 4000); // 3 seconds + 1 second buffer

            return true;
        } catch (error) {
            console.error('Error playing video:', error);
            return false;
        }
    }

    pause() {
        this.videoElement.pause();
    }

    resume() {
        this.videoElement.play();
    }

    stop() {
        this.videoElement.pause();
        this.videoElement.currentTime = 0;
    }

    setOnEndCallback(callback) {
        this.onEndCallback = callback;
    }

    handleVideoEnd() {
        if (this.onEndCallback) {
            this.onEndCallback(this.clipIndex);
        }
    }

    handleError(error) {
        console.error('Video playback error:', error);
        // Implement error handling (e.g., show error message, try next clip, etc.)
    }

    getCurrentTime() {
        return this.videoElement.currentTime;
    }

    getDuration() {
        return this.videoElement.duration;
    }

    setVolume(volume) {
        this.videoElement.volume = Math.max(0, Math.min(1, volume));
    }

    mute() {
        this.videoElement.muted = true;
    }

    unmute() {
        this.videoElement.muted = false;
    }

    seekTo(time) {
        if (time >= 0 && time <= this.videoElement.duration) {
            this.videoElement.currentTime = time;
        }
    }

    isPlaying() {
        return !this.videoElement.paused;
    }

    getClipCount() {
        return this.clips.length;
    }

    getCurrentClipIndex() {
        return this.clipIndex;
    }

    getClipInfo(index) {
        // Return dummy clip info for testing
        return {
            answer: `Correct Answer ${index + 1}`,
            url: this.clips[index] || '/clips/sample.mp4'
        };
    }

    getCurrentClipAnswer() {
        return `Correct Answer ${this.clipIndex + 1}`;
    }
}