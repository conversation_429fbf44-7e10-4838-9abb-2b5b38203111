<!DOCTYPE html>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
     KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
<html lang="en">

<head>
    <meta charset="UTF-8">
    <!--
        Customize this policy to fit your own app's needs. For more guidance, please refer to the docs:
            https://cordova.apache.org/docs/en/latest/
        Some notes:
            * https://ssl.gstatic.com is required only on Android and is needed for TalkBack to function properly
            * Disables use of inline scripts in order to mitigate risk of XSS vulnerabilities. To change this:
                * Enable inline JS: add 'unsafe-inline' to default-src
        -->
    <meta http-equiv="Content-Security-Policy"
        content="default-src 'self' data: https://ssl.gstatic.com 'unsafe-eval'; style-src 'self' 'unsafe-inline'; media-src *; img-src 'self' data: content:;">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light dark">
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <title>Alyami Clips Quiz - TV</title>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Alyami Clips Quiz</h1>
            <p>Connect your smartphones to play!</p>
        </div>

        <div class="menu">
            <div class="menu-item" data-mode="standard" tabindex="0">Standard Mode</div>
            <div class="menu-item" data-mode="easy" tabindex="0">Easy Mode</div>
            <div class="menu-item" data-mode="medium" tabindex="0">Medium Mode</div>
            <div class="menu-item" data-mode="hard" tabindex="0">Hard Mode</div>
        </div>

        <div class="qr-container">
            <div class="qr-code" id="qrCode"></div>
            <p class="qr-text">Scan this QR code with your smartphone to join the game</p>
            <p class="qr-text">Connected Players: <span id="playerCount">0</span></p>
        </div>

        <div class="game-screen">
            <div class="game-info">
                <p>Round: <span id="currentRound">0</span> / <span id="maxRounds">10</span></p>
                <p>Time Remaining: <span id="timer">0</span>s</p>
            </div>
            <div class="video-container">
                <video id="clipPlayer" controls>
                    Your browser does not support the video element.
                </video>
            </div>
        </div>

        <div class="leaderboard">
            <h2>Leaderboard</h2>
            <ul class="leaderboard-list" id="leaderboardList">
                <!-- Leaderboard items will be added dynamically -->
            </ul>
        </div>

        <div id="game-over-screen" class="screen">
            <h2>Game Over!</h2>
            <p id="winner-message"></p>
            <button id="restart-game">Start New Game</button>
        </div>
    </div>

    <!-- cordova.js removed for browser testing -->
    <script src="js/qrcode.min.js"></script>
    <script src="js/videoPlayer.js"></script>
    <script src="js/app.js"></script>
</body>

</html>