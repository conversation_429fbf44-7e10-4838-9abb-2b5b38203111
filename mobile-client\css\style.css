/* General Styles */
body {
    margin: 0;
    padding: 0;
    font-family: 'Roboto', sans-serif;
    background-color: #f0f2f5;
}

#app {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}

.screen {
    display: none;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.screen.active {
    display: block;
}

/* Registration Screen */
#registration-screen {
    text-align: center;
}

#player-name {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.avatar-selection {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 20px 0;
}

.avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 5px;
    cursor: pointer;
    transition: transform 0.2s;
}

.avatar:hover {
    transform: scale(1.1);
}

.avatar.selected {
    border: 3px solid #4CAF50;
}

#join-game {
    width: 100%;
    padding: 12px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#join-game:hover {
    background-color: #45a049;
}

/* Waiting Screen */
.player-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
}

#player-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 10px;
}

/* Question Screen */
.question-info {
    text-align: center;
    margin-bottom: 20px;
}

.question-info h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 18px;
}

.question-info p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.timer-container {
    width: 100%;
    height: 5px;
    background-color: #ddd;
    margin-bottom: 20px;
}

.timer-bar {
    width: 100%;
    height: 100%;
    background-color: #4CAF50;
    transform-origin: left;
}

@keyframes timer {
    from {
        transform: scaleX(1);
    }

    to {
        transform: scaleX(0);
    }
}

#options-container {
    display: grid;
    gap: 10px;
}

.option-btn {
    padding: 15px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s;
}

.option-btn:hover {
    background-color: #f5f5f5;
}

.option-btn.selected {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
}

/* Hard Mode Styles */
.hard-mode {
    grid-template-columns: repeat(2, 1fr);
}

/* Result Screens */
.answer-info {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.correct {
    color: #4CAF50;
}

.wrong {
    color: #f44336;
}

/* Game Over Screen */
.final-stats {
    margin: 20px 0;
    text-align: center;
}

#play-again-btn {
    width: 100%;
    padding: 12px;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#play-again-btn:hover {
    background-color: #1976D2;
}